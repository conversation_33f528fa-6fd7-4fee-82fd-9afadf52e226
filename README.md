# SetTips 代码补全插件

这是一个JetBrains IDE插件，为`setTips()`函数调用提供智能代码补全功能。

## 功能特性

- 当输入`setTips('`时自动触发代码补全
- 从项目根目录的`language.txt`文件中读取key-value对
- 支持模糊匹配，可以根据key或value进行搜索
- 支持单引号和双引号字符串
- 显示key对应的value作为提示信息

## 使用方法

1. 在项目根目录创建`language.txt`文件
2. 在文件中添加key=value格式的条目，例如：
   ```
   welcome=欢迎使用我们的应用
   login=登录
   logout=退出登录
   username=用户名
   password=密码
   ```

3. 在代码中输入`setTips('`时，插件会自动显示可用的补全选项
4. 选择需要的key，插件会自动插入对应的key值

## 示例

```javascript
// 输入 setTips(' 后会显示所有可用的key
setTips('welcome');  // 补全为 welcome

// 输入 setTips('user 会显示包含user的key
setTips('username'); // 补全为 username

// 也支持双引号
setTips("login");    // 补全为 login
```

## 开发和构建

1. 克隆项目
2. 使用IntelliJ IDEA打开项目
3. 运行`./gradlew runIde`来启动带有插件的IDE实例进行测试
4. 运行`./gradlew buildPlugin`来构建插件

## 文件结构

- `src/main/kotlin/com/example/demo/SetTipsCompletionContributor.kt` - 主要的补全逻辑
- `src/main/resources/META-INF/plugin.xml` - 插件配置文件
- `language.txt` - 语言配置文件（示例）
- `test.js` - 测试文件

## 注意事项

- `language.txt`文件必须位于项目根目录
- 文件格式为`key=value`，每行一个条目
- 支持`#`开头的注释行
- key和value不能为空
