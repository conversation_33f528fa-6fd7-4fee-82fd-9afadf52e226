// 测试文件 - 用于验证代码补全功能
// 在 setTips(' 后面输入时应该触发补全

function showMessage() {
    setTips('welcome');
    setTips('login');
    setTips('');  // 在这里输入应该显示所有可用的key
    setTips('user');  // 输入user应该显示username相关的补全
}

function showError() {
    setTips('error');
    setTips('warn');  // 输入warn应该显示warning相关的补全
    setTips('suc');   // 输入suc应该显示success
}

// 也可以测试双引号
function showInfo() {
    setTips("info");
    setTips("success");
    setTips("log");   // 输入log应该显示login, logout
}

// 测试不同的调用方式
class MyClass {
    showTips() {
        this.setTips('home');
        obj.setTips('settings');
        setTips( 'help' );  // 有空格的情况
    }
}
