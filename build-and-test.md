# 构建和测试指南

## 构建插件

1. **构建插件**
   ```bash
   ./gradlew buildPlugin
   ```
   构建完成后，插件文件将在 `build/distributions/` 目录中。

2. **运行带插件的IDE进行测试**
   ```bash
   ./gradlew runIde
   ```
   这将启动一个带有你的插件的IntelliJ IDEA实例。

## 测试步骤

1. **启动测试IDE**
   运行 `./gradlew runIde` 后，会打开一个新的IntelliJ IDEA窗口。

2. **创建测试项目**
   - 在新IDE中创建一个新项目或打开现有项目
   - 在项目根目录创建 `language.txt` 文件
   - 添加一些key=value条目

3. **测试代码补全**
   - 创建一个JavaScript文件（或任何文本文件）
   - 输入 `setTips('` 
   - 按 `Ctrl+Space` 触发代码补全
   - 应该看到来自language.txt的key列表

## 示例测试场景

### 1. 基本补全测试
```javascript
function test() {
    setTips(''); // 光标在引号内，按Ctrl+Space应显示所有key
}
```

### 2. 前缀匹配测试
```javascript
function test() {
    setTips('log'); // 输入log，应显示login, logout等
}
```

### 3. 双引号测试
```javascript
function test() {
    setTips("user"); // 双引号也应该工作
}
```

## 调试

如果补全不工作，检查：

1. **language.txt文件位置**
   - 确保文件在项目根目录
   - 检查文件格式是否正确（key=value）

2. **插件是否正确加载**
   - 在IDE中检查 Settings > Plugins
   - 确保"SetTips Code Completion"插件已启用

3. **日志检查**
   - 查看IDE日志文件中的错误信息
   - 日志位置通常在 `build/idea-sandbox/system/log/idea.log`

## 运行单元测试

```bash
./gradlew test
```

这将运行 `SetTipsCompletionTest` 中的测试用例。
