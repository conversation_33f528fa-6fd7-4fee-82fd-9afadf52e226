package com.example.demo

import java.io.IOException

object TipLoader {
    private const val FILE_NAME = "language.txt"

    fun loadTips(): Map<String, String> {
        val tips = mutableMapOf<String, String>()
        try {
            val lines = this::class.java.classLoader.getResource(FILE_NAME).readText().lines()
            lines.forEach { line ->
                if (!line.startsWith("#") && line.contains("=")) {
                    val (key, value) = line.split("=")
                    tips[key] = value
                }
            }
        } catch (e: IOException) {
            println("Failed to load tips: ${e.message}")
        }
        return tips
    }
}
